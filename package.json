{"name": "jtny-ganneng-gatdf-web", "version": "4.5.0", "private": true, "scripts": {"dev": " vite dev", "serve": "pnpm dev", "build": "rimraf dist.zip && vite build", "build:test": "rimraf dist.zip && vite build --mode test", "build:pro": "rimraf dist.zip && vite build --mode production", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f src/assets/svg -o src/assets/svg", "cloc": "NODE_OPTIONS=--max-old-space-size=4096 cloc . --exclude-dir=node_modules --exclude-lang=YAML", "clean:cache": "rimraf node_modules && rimraf .eslintcache && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint \"**/*.{html,vue,css,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "preinstall": "npx only-allow pnpm"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@handsontable/vue3": "^14.2.0", "@pureadmin/descriptions": "^1.1.1", "@pureadmin/table": "^2.3.2", "@pureadmin/utils": "^1.9.6", "@vueuse/core": "^10.2.0", "@vueuse/motion": "^2.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^1.4.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "echarts": "^5.4.2", "element-plus": "2.3.6", "handsontable": "^14.2.0", "hyperformula": "^2.6.2", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.1.4", "pinia-plugin-persistedstate": "^3.2.1", "pinyin-pro": "^3.15.2", "qs": "^6.11.2", "responsive-storage": "^2.2.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-demi": "^0.14.6", "vue-router": "^4.2.2", "vue-types": "^5.1.0", "vue3-seamless-scroll": "^2.0.1", "vue3-slide-verify": "^1.1.4", "vxe-pc-ui": "4.2.23", "vxe-table": "4.7.91", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yarn": "^1.22.22", "mockjs": "^1.1.0"}, "devDependencies": {"@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.9", "@iconify/vue": "^4.1.1", "@pureadmin/theme": "^3.1.0", "@types/js-cookie": "^3.0.3", "@types/mockjs": "^1.0.7", "@types/node": "^20.3.1", "@types/nprogress": "0.2.0", "@types/qs": "^6.9.7", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "autoprefixer": "^10.4.14", "cloc": "^2.11.0", "cross-env": "^7.0.3", "cssnano": "^6.0.1", "eslint": "^8.43.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.15.1", "less": "^4.2.0", "picocolors": "^1.0.0", "postcss": "^8.4.24", "postcss-html": "^1.5.0", "postcss-import": "^15.1.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.8", "rimraf": "^5.0.1", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.77.8", "sass-loader": "^13.3.2", "stylelint": "^15.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.2.0", "stylelint-config-recommended": "^12.0.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^9.0.0", "stylelint-order": "^6.0.3", "stylelint-prettier": "^3.0.0", "stylelint-scss": "^5.0.1", "svgo": "^3.0.2", "tailwindcss": "^3.3.2", "terser": "^5.18.1", "typescript": "5.0.4", "vite": "^4.5.3", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "2.9.6", "vite-plugin-remove-console": "^2.1.1", "vite-svg-loader": "^4.0.0", "vue-eslint-parser": "^9.3.1", "vue-tsc": "^1.8.1", "vue3-scroll-seamless": "^1.0.6", "x-data-spreadsheet": "^1.1.9"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup", "webpack", "core-js"]}, "allowedDeprecatedVersions": {"sourcemap-codec": "*", "w3c-hr-time": "*", "stable": "*"}}, "repository": "**************:pure-admin/pure-admin-thin.git", "author": "jtny", "license": "MIT"}