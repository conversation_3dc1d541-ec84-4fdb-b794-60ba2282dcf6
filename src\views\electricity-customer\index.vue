<template>
  <section>
    <div class="app-search-card">
      <div class="app-form-group">
        <div class="ml-[20px]">
          <span>用电户号：</span>
          <el-input
            v-model="searchInfo.accountNo"
            clearable
            placeholder="请输入用电户号"
            class="filter-item"
          />
        </div>
        <div class="ml-[20px]">
          <span>客户名称：</span>
          <el-input
            v-model="searchInfo.customName"
            clearable
            placeholder="请输入客户名称"
            class="filter-item"
          />
        </div>
        <div class="ml-[20px]">
          <span>用电地址：</span>
          <el-input
            v-model="searchInfo.address"
            clearable
            placeholder="请输入用电地址"
            class="filter-item"
          />
        </div>
      </div>
      <div class="app-btn-group">
        <el-button class="filter-item" type="primary" @click="getList"
          >查询</el-button
        >
        <el-button class="filter-item" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="app-card mt-[20px] p-[20px] pt-[10px]">
      <div
        class="mb-[10px]"
        style="display: flex; justify-content: space-between"
      >
        <el-button type="primary" @click="handleCreate">新增</el-button>
        <el-row align="middle">
          <el-button @click="DownloadTemplate" type="success" :icon="Bottom">
            模板下载
          </el-button>
          &nbsp;
          <el-upload
            :http-request="{}"
            action=""
            :on-change="electricUploadFile"
            :show-file-list="false"
          >
            <el-button :icon="Upload" type="primary">数据导入</el-button>
          </el-upload>
        </el-row>
      </div>
      <pure-table
        :columns="columns"
        border
        stripe
        :loading="loading"
        :data="tableData"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #accountNoHeader>
          <table-head-popover
            :time-slot="timeSlot"
            @tableUpdate="handleTableUpdate"
            :column="columns[1]"
          />
        </template>
        <template #customNameHeader>
          <table-head-popover
            :time-slot="timeSlot"
            @tableUpdate="handleTableUpdate"
            :column="columns[4]"
          />
        </template>
        <template #meterCodeHeader>
          <table-head-popover
            :time-slot="timeSlot"
            @tableUpdate="handleTableUpdate"
            :column="columns[5]"
          />
        </template>
        <template #accountNo="{ row }">
          <a @click="getDetail(row.meterId)" style="color: #007bf7">{{
            row.accountNo
          }}</a>
        </template>
        <template #operation="{ row }">
          <el-button
            link
            type="primary"
            size="small"
            @click="getDetail(row.meterId)"
            >编辑</el-button
          >
          <el-popconfirm
            width="220"
            confirm-button-text="确定"
            cancel-button-text="取消"
            :icon="InfoFilled"
            icon-color="#626AEF"
            title="确认删除？"
            @confirm="handleDel(row.meterId)"
          >
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </div>
    <el-dialog
      v-model="dialogVisible"
      destroy-on-close
      :title="title"
      width="60%"
    >
      <el-form
        ref="ruleFormRef"
        :model="formInline"
        :rules="dataFormRules"
        label-width="160px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="用电户号：" prop="accountNo">
              <el-input
                maxlength="20"
                show-word-limit
                v-model="formInline.accountNo"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电地址：" prop="address">
              <el-input
                maxlength="20"
                show-word-limit
                v-model="formInline.address"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基本电费计费方式：" prop="billingMethod">
              <DictSelect
                v-model="formInline.billingMethod"
                :clearable="true"
                dict-code="billingType"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属客户：" prop="customName">
              <el-input
                @click="selectVisible = true"
                v-model="formInline.customName"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申报类型：" prop="declareType">
              <DictSelect
                v-model="formInline.declareType"
                :clearable="true"
                dict-code="declareType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否光伏：" prop="isPhotovoltaic">
              <el-radio-group v-model="formInline.isPhotovoltaic" class="ml-4">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="户号对应营销密码：" prop="marketingPassword">
              <el-input
                maxlength="18"
                show-word-limit
                type="password"
                show-password
                v-model="formInline.marketingPassword"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交易平台登录账号：" prop="tradingAccount">
              <el-input
                maxlength="18"
                show-word-limit
                v-model="formInline.tradingAccount"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交易平台登录密码：" prop="tradingPassword">
              <el-input
                maxlength="18"
                show-word-limit
                type="password"
                show-password
                v-model="formInline.tradingPassword"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="变压器容量：" prop="transformerCapacity">
              <el-input
                maxlength="10"
                show-word-limit
                :min="0"
                v-model="formInline.transformerCapacity"
                placeholder="请输入"
                type="number"
              >
                <template #append>KVA</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电类型：" prop="useType">
              <DictSelect
                v-model="formInline.useType"
                :clearable="true"
                dict-code="electricityType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电压等级：" prop="voltageClass">
              <el-input-number
                maxlength="10"
                :min="0"
                show-word-limit
                v-model="formInline.voltageClass"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="计量点ID" prop="meterCode">
              <el-input
                maxlength="30"
                show-word-limit
                v-model="formInline.meterCode"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电性质" prop="meterNature">
              <!--              <DictSelect-->
              <!--                  v-model="formInline.meterNature"-->
              <!--                  value="meterNature"-->
              <!--                  :clearable="true"-->
              <!--                  dict-code="natureOfElectricity"-->
              <!--              />-->
              <el-select v-model="formInline.meterNature" placeholder="请选择">
                <el-option
                  v-for="item in natureOfElectricityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <el-input
                maxlength="30"
                show-word-limit
                type="textarea"
                v-model="formInline.remark"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      width="60%"
      destroy-on-close
      append-to-body
      v-model="selectVisible"
      title="客户选择"
    >
      <power-consumer @change="handleClose" />
    </el-dialog>
  </section>
</template>

<script lang="ts">
import { ref, reactive, onMounted } from "vue";
import powerConsumer from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import TableHeadPopover from "@/components/Core/TableHeadPopover/index.vue";
import { columns } from "./components/data";
import type { PaginationProps } from "@pureadmin/table";
import { delay } from "@pureadmin/utils";
import { Bottom, InfoFilled, Upload } from "@element-plus/icons-vue";
import {
  saveElectricityAccountApi,
  getElectricityAccountListApi,
  getElectricityAccountDetailApi,
  delElectricityAccountByIdApi,
  editElectricityAccountApi,
  downloadElectrictyTemplateAPI,
  importElectrictyDataAPI
} from "@/api/customer-management/index";
import {
  CustomerAccountListModel,
  ElectricityAccountPageModel
} from "@/model/customerModel";
import type { FormInstance } from "element-plus";
import { ElLoading, ElMessage } from "element-plus";
import { attachmentDownload, importRealtimeForcastData } from "@/api";
export default {
  name: "ElectricityCustomer",
  computed: {
    Upload() {
      return Upload;
    },
    Bottom() {
      return Bottom;
    }
  },
  components: {
    powerConsumer,
    TableHeadPopover
  },
  setup() {
    onMounted(async () => {
      getList();
    });
    const ruleFormRef = ref<FormInstance>();
    const title = ref<string>("新增");
    const loading = ref(false);
    const dialogVisible = ref<boolean>(false);
    const selectVisible = ref<boolean>(false);
    const tableData = ref([]);
    const timeSlot = ref<number>(Date.now());
    const natureOfElectricityOptions = ref([
      {
        label: "大工业用电",
        value: "大工业用电"
      },
      {
        label: "非工业",
        value: "非工业"
      },
      {
        label: "非居民照明",
        value: "非居民照明"
      },
      {
        label: "普通工业",
        value: "普通工业"
      },
      {
        label: "商业用电",
        value: "商业用电"
      }
    ]);
    const checkNum = (rule: any, value: any, callback: any) => {
      if (!Number(value)) {
        callback();
      }
      if (!Number(value)) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    const checkNoEmptyNum = (rule: any, value: any, callback: any) => {
      if (!value) {
        callback(new Error("用电户号是必填项"));
      }
      if (!Number(value)) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    const dataFormRules = {
      accountNo: [
        { required: true, validator: checkNoEmptyNum, trigger: "blur" }
      ],
      customName: [
        {
          required: true,
          message: "所属客户是必填项",
          trigger: "change"
        }
      ],
      meterCode: [
        {
          required: true,
          message: "计量点ID是必填项",
          trigger: "change"
        }
      ],
      meterNature: [
        {
          required: true,
          message: "用电性质是必填项",
          trigger: "change"
        }
      ],
      // transformerCapacity: [
      //   {
      //     validator: checkNum,
      //     trigger: "blur"
      //   }
      // ],
      voltageClass: [
        {
          validator: checkNum,
          trigger: "blur"
        }
      ]
    };
    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
      pageSize: 10,
      currentPage: 1,
      pageSizes: [10, 20, 40, 50],
      total: 0,
      align: "right",
      background: true,
      small: false
    });
    const searchInfo = reactive<ElectricityAccountPageModel>({
      accountNo: undefined,
      customName: undefined,
      address: undefined,
      pageNo: 1,
      pageSize: 10
    });
    const formMap: CustomerAccountListModel = {
      id: undefined,
      accountNo: "",
      billingMethod: "",
      customId: undefined,
      customName: undefined,
      declareType: undefined,
      isPhotovoltaic: undefined,
      marketingPassword: "",
      tradingAccount: "",
      tradingPassword: "",
      transformerCapacity: "",
      useType: undefined,
      voltageClass: undefined,
      remark: undefined,
      address: ""
    };
    const formInline = ref<CustomerAccountListModel>({
      id: undefined,
      accountNo: "",
      billingMethod: "",
      customId: undefined,
      customName: undefined,
      declareType: undefined,
      isPhotovoltaic: undefined,
      marketingPassword: "",
      tradingAccount: "",
      tradingPassword: "",
      transformerCapacity: "",
      useType: undefined,
      voltageClass: undefined,
      remark: undefined,
      address: "",
      meterCode: "",
      meterNature: ""
    });

    async function getList() {
      loading.value = true;
      const { data } = await getElectricityAccountListApi(searchInfo);
      pagination.total = Number(data.totalCount);
      tableData.value = data.data;
      delay(600).then(() => {
        loading.value = false;
      });
    }
    function handleReset() {
      searchInfo.accountNo = undefined;
      searchInfo.customName = undefined;
      searchInfo.address = undefined;
      timeSlot.value = Date.now();
      getList();
    }

    function onSizeChange(val) {
      searchInfo.pageSize = val;
      getList();
    }

    function onCurrentChange(val) {
      searchInfo.pageNo = val;
      getList();
    }

    // 表格筛选
    function handleTableUpdate(data) {
      searchInfo[data.propKey] = data.value;
      getList();
    }

    async function getDetail(id: number) {
      title.value = "编辑";
      dialogVisible.value = true;
      const res = await getElectricityAccountDetailApi(id);
      formInline.value = { ...res.data };
    }

    async function handleDel(id: number) {
      console.log(id);
      await delElectricityAccountByIdApi(id);
      ElMessage({
        message: "操作成功",
        type: "success"
      });
      getList();
    }

    async function submit(formEl: FormInstance | undefined) {
      if (!formEl) return;
      await formEl.validate(async (valid, fields) => {
        if (valid) {
          if (title.value == "新增") {
            console.log(formInline.value);
            const res = await saveElectricityAccountApi(formInline.value);
            if (res.code === "200") {
              ElMessage({
                message: "操作成功",
                type: "success"
              });
              await getList();
              dialogVisible.value = false;
            } else {
              ElMessage({
                message: res.message,
                type: "success"
              });
            }
          } else if (title.value === "编辑") {
            const res = await editElectricityAccountApi(formInline.value);
            if (res.code === "200") {
              ElMessage({
                message: "操作成功",
                type: "success"
              });
              await getList();
              dialogVisible.value = false;
            } else {
              ElMessage({
                message: res.message,
                type: "success"
              });
            }
          }
        } else {
          console.log("error submit!", fields);
        }
      });
    }
    // 客户确认选择弹框事件
    function handleClose(row) {
      selectVisible.value = false;
      formInline.value.customId = row.id;
      formInline.value.customName = row.name;
    }
    function handleCreate() {
      title.value = "新增";
      Object.assign(formInline.value, formMap);
      dialogVisible.value = true;
    }

    // 下载模板
    function DownloadTemplate() {
      const loading = ElLoading.service({ text: "正在下载..." });
      downloadElectrictyTemplateAPI()
        .then(data => {
          const blob = new Blob([data]);
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = "用电户号_导入模板.xlsx";
          link.click();
          window.URL.revokeObjectURL(url);
          loading.close();
        })
        .catch(() => {
          ElMessage.error("下载失败");
          loading.close();
        });
    }

    // 导入
    const dayinScencefileList = ref<any>([]);
    // 导入日内预测价格文件上传状态改变
    function electricUploadFile(file: any) {
      const fileExtension = file.name.split(".").pop();
      const size = file.size / 1024 / 1024;
      if (fileExtension !== "xlsx" && fileExtension !== "xls") {
        ElMessage.warning("只能上传excel文件");
        dayinScencefileList.value = [];
        return;
      }
      if (size > 10) {
        ElMessage.warning("文件大小不得超过10M");
        dayinScencefileList.value = [];
        return;
      }
      dayinScencefileList.value = [file];
      dayinScencehandleUpload();
    }
    // 上传函数
    const dayinScencehandleUpload = () => {
      if (dayinScencefileList.value.length === 0) {
        ElMessage.warning("未选择文件");
      } else {
        const formData = new FormData();
        formData.append("file", dayinScencefileList.value[0].raw);
        importElectrictyDataAPI(formData)
          .then(() => {
            // ElMessage.success('上传成功')
            // importElectrictyDataAPI()
            getList();
          })
          .catch((e: any) => {
            console.log(e);
          });
      }
    };
    return {
      InfoFilled,
      submit,
      getList,
      getDetail,
      handleDel,
      handleReset,
      loading,
      dialogVisible,
      pagination,
      searchInfo,
      onSizeChange,
      onCurrentChange,
      columns,
      tableData,
      title,
      handleCreate,
      formInline,
      dataFormRules,
      ruleFormRef,
      handleClose,
      selectVisible,
      natureOfElectricityOptions,
      DownloadTemplate,
      electricUploadFile,
      timeSlot,
      handleTableUpdate
    };
  }
};
</script>

<style lang="scss" scoped></style>
