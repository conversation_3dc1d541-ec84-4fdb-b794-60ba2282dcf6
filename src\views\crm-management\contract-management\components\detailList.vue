<template>
  <div>
    <el-dialog v-model="dialogVisible" title="详情" width="60%">
      <el-form ref="ruleFormRef" :model="dataForm" :rules="dataFormRules" label-width="160px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="零售用户：" prop="retailUser">
              <el-input @click="selectVisible = true" v-model="dataForm.retailUser" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同编号:" prop="contractNum">
              <el-input  v-model="dataForm.contractNum" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同电量:" prop="contractElectricity">
              <el-input  v-model="dataForm.contractElectricity" placeholder="请选择">
                <template #append>
                  kWh
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="合同开始时间：" prop="startTime">
              <el-date-picker style="width: 100%" v-model="dataForm.startTime" type="month" placeholder="请选择合同开始时间"
              value-format="YYYY-MM" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同结束时间：" prop="endTime">
              <el-date-picker style="width: 100%" v-model="dataForm.endTime" type="month" placeholder="请选择合同结束时间"
                value-format="YYYY-MM" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下单时间:" prop="orderTime">
              <!-- <el-input  v-model="dataForm.orderTime" placeholder="请选择" /> -->
              <el-date-picker style="width: 100%" v-model="dataForm.orderTime" type="datetime" placeholder="请选择下单时间"
                value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="套餐名称：" prop="packageName">
              <el-input  v-model="dataForm.packageName" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="代理消费模型:" prop="agentSaleModel">
              <el-input  v-model="dataForm.agentSaleModel" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="营销人员:" prop="saleManId">
              <el-select clearable @change="handleSelect" style="margin-right: 10px; " class="singleSelect"
            v-model="dataForm.saleManId" placeholder="营销人员">
            <el-option v-for="item in Marketing  " :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <el-input maxlength="30" show-word-limit type="textarea" v-model="dataForm.remark" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="签约时间：" prop="signTime">
              <el-date-picker style="width: 100%" v-model="dataForm.signTime" type="date" placeholder="请选择"
                value-format="x" />
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item label="交易开始日期：" prop="startTime">
              <el-date-picker style="width: 100%" v-model="dataForm.startTime" type="date" placeholder="开始日期"
                value-format="x" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易截止日期：" prop="endTime">
              <el-date-picker style="width: 100%" v-model="dataForm.endTime" type="date" placeholder="结束日期"
                value-format="x" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="跟踪人：" prop="entryld">
              <el-select style="width: 100%" @change="handleSelect" v-model="dataForm.entryId" filterable
                placeholder="请选择">
                <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合同电价：" prop="electricityPrice">
              <el-input v-model="dataForm.electricityPrice" placeholder="请选择">
                <template #append>元/MWh</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同电量：" prop="electricityQty">
              <el-input maxlength="10" show-word-limit v-model="dataForm.electricityQty" placeholder="请输入">
                <template #append>MWh</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <div class="ml-20">
            <el-checkbox v-model="checkedMonth" label="分月电量" />
            <el-checkbox v-model="checkedHour" label="分时电量" />
          </div>
        </el-row>
        <el-row v-if="checkedMonth || checkedHour">
          <pure-table :columns="addColumns" :data="addTableData">
            <template v-for="item in monthSlots" :key="item" #[`month${item}`]="{ row }">
              <el-input @change="handleChange(item)" v-model="row[`month${item}`]" />
            </template>
          </pure-table>
        </el-row>
        <el-row class="mt-5">
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <el-input maxlength="30" show-word-limit type="textarea" v-model="dataForm.remark" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="合同附件：" prop="fileList">
              <el-upload v-model:file-list="fileList" with-credentials :headers="header" :data="uploadData"
                         :action="actionUrl" :before-upload="beforeUpload" :on-preview="handleDownload" :on-remove="handleRemove">
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持所有格式，且大小不超过10M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!--            选择商机数据弹窗列表关联-->
            <el-form-item label="商机数据关联：">
              <el-row>
                <el-input style="width: 70%" v-model="dataForm.customOpportunityTitle" placeholder="请选择" />
                <el-button type="primary" @click="handleSelectBusinessDialog">选择</el-button>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog width="60%" append-to-body destroy-on-close v-model="selectVisible" title="客户选择">
      <customer-select @change="handleClose" />
    </el-dialog>

    <el-dialog width="70%" append-to-body destroy-on-close v-model="selectBusinessVisible" title="商机选择">
      <business-opportunity-select @change="handleSelectBusiness" />
      <!-- <div class="flex justify-end mt-[40px]">
        <el-button type="primary" @click="selectBusinessVisible = false"
          >确认选择</el-button
        >
      </div> -->
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import { addColumns } from "./data";
import dayjs from "dayjs";
import CustomerSelect from "@/components/Core/PowerConsumerSelect/powerConsumer.vue";
import BusinessOpportunitySelect from "@/components/Core/BusinessOpportunitySelect/index.vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { addContractData } from "@/api/customer-management/index";
import { getAllUserListApi, getUnqueIdApi } from "@/api/user";
import type { UploadUserFile } from "element-plus";
import { useFileAction } from "@/hooks/fileAction/useFileAction";
import { cloneDeep } from "lodash-es";
import { CustomerContractModel } from "@/model/customerModel";
import {
  getAllSalesmanList,contractDataUpdate //获取营销人员列表
} from '@/api'
const emit = defineEmits(["update"]);
const dialogVisible = ref<boolean>(false);
const selectBusinessVisible = ref<boolean>(false);
const { header, beforeUpload, actionUrl,getFileList ,fileList,handleRemove, handleDownload} = useFileAction();

function handleSelectBusinessDialog() {
  selectBusinessVisible.value = true;
}
// 合同协议上传额外参数
const uploadData = ref({
  type: "1",
  id: undefined
});
// 合同附件列表
// const fileList = ref<UploadUserFile[]>([]);
// 月份循环插槽
const monthSlots = new Array(12)
  .fill(item => item)
  .map((_, index) => `${index + 1}`);
const addTableData = ref([
  {
    type: "总",
    month1: "",
    month2: "",
    month3: "",
    month4: "",
    month5: "",
    month6: "",
    month7: "",
    month8: "",
    month9: "",
    month10: "",
    month11: "",
    month12: ""
  }
]);
const title = ref<string>("新增合同");
// 分月电量
const checkedMonth = ref<boolean>(false);
// 分时电量
const checkedHour = ref<boolean>(false);
const selectVisible = ref<boolean>(false);
const ruleFormRef = ref<FormInstance>();
const checkNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请输入"));
  }
  if (!Number(value)) {
    callback(new Error("请输入数字"));
  } else {
    callback();
  }
};

function validateFileList(rule, value, callback) {
  nextTick(() => {
    console.log(value,fileList.value)
    if (fileList.value.length === 0) {
      callback(new Error('请上传至少一个文件'));
    } else {
      callback();
    }
  })
}
// 表单校验
const dataFormRules = {
  retailUser: [
    {
      required: true,
      message: "零售用户是必填项",
      trigger: "change"
    }
  ],
  contractNum: [
    {
      required: true,
      message: "合同编号是必填项",
      trigger: "blur"
    }
  ],
  contractElectricity: [
    {
      required: true,
      message: "合同电量是必填项",
      trigger: "blur"
    }
  ],
  startTime: [
    {
      required: true,
      message: "合同开始时间是必填项",
      trigger: "blur"
    }
  ],
  endTime: [
    {
      required: true,
      message: "合同结束时间是必填项",
      trigger: "blur"
    }
  ],
  orderTime: [
    {
      required: true,
      message: "下单时间是必填项",
      trigger: "blur"
    }
  ],
  packageName: [
    {
      required: true,
      message: "套餐名称是必填项",
      trigger: "blur"
    }
  ],
  agentSaleModel: [
    {
      required: true,
      message: "代理消费模型是必填项",
      trigger: "blur"
    }
  ],
  saleManId: [
    {
      required: true,
      message: "营销人员是必填项",
      trigger: "blur"
    }
    ],
  fileList: [
    {
      required: true,
      validator: validateFileList,
      trigger: "change"
    }
  ]
};
const type = ["尖", "峰", "平", "谷"];
// 新增表单对象
const dataForm = ref<any>({
  id: undefined,
  retailUser: "",
  contractNum: "",
  contractElectricity: "",
  startTime: "",
  endTime: "",
  orderTime: "",
  packageName: "",
  agentSaleModel: "",
  saleMan: "",
  saleManId: "",
  entryName: "",
  remark:"",
  fileId:""

  // electricityQty: undefined,
  // remark: "",
  // isByMonth: false,
  // isByPeriod: false,
  // status: 8, // status=8为新增标识
  // items: {
  //   total: [], /// 总
  //   flat: [], // 平
  //   peak: [], // 峰
  //   top: [], // 尖
  //   valley: [] // 谷
  // }
});
const dataFormMap = cloneDeep(dataForm.value);

// 合同跟进人下拉
const optionsList = ref([]);

const Marketing = ref<any>([]);
  async function getAllSalesmanListInfo() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    Marketing.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })

  }
}
function handleSelect(data) {
  console.log(data, "data");
  const name = Marketing.value.find(i => i.value == data).label;
  dataForm.value.saleMan = name;
}
function handleChange(column) {
  // 如果勾选分时段电量则自动计算填入汇总行
  if (checkedHour.value) {
    const total =
      Number(addTableData.value[1][`month${column}`]) +
      Number(addTableData.value[2][`month${column}`]) +
      Number(addTableData.value[3][`month${column}`]) +
      Number(addTableData.value[4][`month${column}`]);
    addTableData.value[0][`month${column}`] = String(total);
  }
}
async function getUploadFileId(id:any) {
  // 生成上传的唯一id
  // const res = await getUnqueIdApi();
  uploadData.value.id = id;
}

async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (
        dayjs(dataForm.value.startTime).valueOf() >
        dayjs(dataForm.value.endTime).valueOf()
      ) {
        ElMessage({
          message: "交易开始时间不能大于交易结束时间！",
          type: "error"
        });
        return;
      }
  
 contractDataUpdate(dataForm.value).then(res=>{
  ElMessage({
          message: "操作成功",
          type: "success"
        });
        emit("update");
        dialogVisible.value = false;
 })
 
    
    
     
    } else {
      console.log("error submit!", fields);
    }
  });
}
// 商机确认选择事件
function handleSelectBusiness(row) {
  // console.log(row, 'row');
  selectBusinessVisible.value = false;
  dataForm.value.customOpportunityId = row.id;
  dataForm.value.customOpportunityTitle = row.title;
}
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  dataForm.value.customId = row.id;
  dataForm.value.customName = row.name;
  dataForm.value.retailUser = row.name;
}
// 获取所有用户
async function getUserList() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
}
// async function getUserList() {
//   const res = await getAllUserListApi();
//   if (res.data) {
//     optionsList.value = res.data.map(item => {
//       return {
//         label: item.name,
//         value: String(item.id)
//       };
//     });
//   }
// }
async function handleCreate(item: any) {

  console.log("item",item);
 
  dialogVisible.value = true;
  getAllSalesmanListInfo();
  getFileList("1", item.id);

 
  await nextTick()
  ruleFormRef.value.resetFields();
  dataForm.value.status = 8;
  fileList.value = [];
  await getUploadFileId(item.id);
  Object.assign(dataForm.value, dataFormMap);
  checkedMonth.value = false;
  checkedHour.value = false;
  addTableData.value = [
    {
      type: "总",
      month1: "",
      month2: "",
      month3: "",
      month4: "",
      month5: "",
      month6: "",
      month7: "",
      month8: "",
      month9: "",
      month10: "",
      month11: "",
      month12: ""
    }
  ];
  dataForm.value.id = uploadData.value.id;
  dataForm.value.retailUser = item.retailUser;
  dataForm.value.contractNum = item.contractNum;
  dataForm.value.contractElectricity = item.contractElectricity;
  dataForm.value.startTime = item.startTime;
  dataForm.value.endTime = item.endTime;
  dataForm.value.orderTime = item.orderTime;
  dataForm.value.packageName = item.packageName;
  dataForm.value.agentSaleModel = item.agentSaleModel;
  dataForm.value.saleManId = item.saleManId;
  dataForm.value.customOpportunityId = item.customOpportunityId;
  dataForm.value.customOpportunityTitle = item.customOpportunityTitle;

  // dataForm.value.saleManId = item.saleMan;
  
  dataForm.value.remark = item.remark;
  dataForm.value.id = item.id;
  // console.log("dataFormsads", item.saleMan,dataForm.value.saleMan)
  
  // getUserList();
}
watch(
  () => checkedHour.value,
  newVal => {
    if (newVal === true) {
      checkedMonth.value = true;
      type.forEach((item, index) => {
        addTableData.value.push({
          type: item,
          month1: "",
          month2: "",
          month3: "",
          month4: "",
          month5: "",
          month6: "",
          month7: "",
          month8: "",
          month9: "",
          month10: "",
          month11: "",
          month12: ""
        });
      });
    } else if (newVal === false) {
      addTableData.value = [
        {
          type: "总",
          month1: "",
          month2: "",
          month3: "",
          month4: "",
          month5: "",
          month6: "",
          month7: "",
          month8: "",
          month9: "",
          month10: "",
          month11: "",
          month12: ""
        }
      ];
    }
  }
);
defineExpose({
  handleCreate
});
</script>
<style lang="scss" scoped></style>
