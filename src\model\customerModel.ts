import {
  BasicFetchResult,
  BaseRequestParams,
  ResponseDetailModel
} from "./baseModel";
export type GetCustomerListResultModel =
  BasicFetchResult<CustomerListResultModel>;
export type GetElectricityAccountListResultModel = BasicFetchResult<
  CustomerAccountListModel[]
>;
export type GetBindListResultModel = BasicFetchResult<CustomerBindListModel[]>;
export type GetFollowListResultModel = BasicFetchResult<
  CustomerFollowListModel[]
>;
export type CustomerValueAddedListResultModel = BasicFetchResult<
  CustomerValueAddedListModel[]
>;
export type GetCustomerOpportunityListResultModel = BasicFetchResult<
  CustomerOpportunityListModel[]
>;
export type GetCustomerContractModelListResultModel = BasicFetchResult<
  CustomerContractModel[]
>;
export type GetCustomMeterListResultModel = BasicFetchResult<
  CustomMeterListModel[]
>;
export type CustomerPortraitLoadResultModel =
  ResponseDetailModel<CustomerPortraitLoadListModel>;

export type CustomerSumResultModel = ResponseDetailModel<CustomerSumModel[]>;

export type AreaSumResultModel = ResponseDetailModel<AreaSumModel[]>;
// 客户画像负荷及波动情况列表
export type CustomerPortraitLoadListModel = {
  timePeriodList: TimePeriodListModel[];
  maxMinList: MaxMinListModel[];
  curveList: CurveListModel[];
};
export type TimePeriodListModel = {
  name: string;
  electricityConsumption: number | string;
  ratio: number | string;
};
export type MaxMinListModel = {
  date: number;
  max: number;
  min: number;
  avg: number;
};
export type CurveListModel = {
  time: string;
  load: number;
  wave: number;
  positiveDeviation: number;
  negativeDeviation: number;
};
// 表记电量列表
export type CustomMeterListModel = {
  id?: number;
  customId?: number;
  customName?: string;
  accountId?: number;
  accountNo?: string;
  meterId?: number;
  meterCode?: string;
  meterName?: string;
  readDate?: number;
  dayTota?: number;
};
// 用电户号列表
export type CustomerAccountListModel = {
  accountNo?: string;
  address?: string;
  billingMethod?: string;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  customId?: number;
  customName?: string;
  declareType?: number;
  id?: number;
  isPhotovoltaic?: boolean;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  marketingPassword?: string;
  remark?: string;
  status?: number;
  tradingAccount?: string;
  tradingPassword?: string;
  transformerCapacity?: number;
  useType?: number;
  voltageClass?: number;
  meterCode?: string;
  meterNature?: string;
};
// 客户基础资料
export type CustomerBasicModel = {
  id?: number | string;
  name?: string;
  agentType?: string | number;
  alias?: string;
  annualElectricity?: number;
  annualProxyElectricity?: string | number;
  areaId?: string;
  areaName?: string;
  bankAccount?: string;
  bankName?: string;
  contact?: string;
  industryId?: number;
  createTime?: string;
  creatorId?: string;
  creatorName?: string;
  customGrade?: number;
  customIdentity?: number;
  customType?: number;
  description?: string;
  followerId?: string;
  followerName?: string;
  formerName?: string;
  greenDemand?: string;
  isOpenSea?: boolean;
  isOutside?: boolean;
  isSign?: boolean;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  membershipGroup?: string;
  ownership?: number;
  registeredCapital?: string;
  socialCreditCode?: string;
  status?: number;
  terminationDate?: number;
  effectiveDate?: number;
  registrationNo?: string;
  legalRepresentative?: string;
  businessTerm?: string;
  businessRegistrationDate?: number;
  issueDate?: number;
  registrationAuthority?: string;
  registrationStatus?: number;
  registeredAddress?: string;
  businessScope?: string;
  electricalNature?: number;
  mainBusiness?: string;
  monthlyAverageElectricity?: number;
  signMonthPeriod?: string;
  thisYearBindPeriod?: string;
  priceModel?: string;
};
// 绑定关系列表;
export type CustomerBindListModel = {
  bindEnd?: number;
  bindStart?: number;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  customElectricityId?: number;
  customElectricityName?: string;
  customMiddlemanId?: number;
  customMiddlemanName?: string;
  id?: number;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  remark?: string;
  status?: number;
};
// 联系人列表
export type CustomerContactListModel = {
  birthday?: number;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  customId?: number;
  customName?: string;
  email?: string;
  fax?: string;
  fixedTelephone?: string;
  id: number;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  mobile?: string;
  name?: string;
  post?: string;
  qq?: string;
  remark?: string;
  role?: string;
  sex?: number;
  status?: number;
  wechat?: string;
};
// 合同列表
export type CustomerContractModel = {
  changeList?: CustomerChangeListModel[];
  code?: string;
  contractStatus?: number;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  customId?: number;
  customName?: string;
  electricityPrice?: number;
  electricityQty?: number;
  endTime?: number;
  entryId?: string;
  entryName?: string;
  id?: number;
  isByMonth?: boolean;
  isByPeriod?: boolean;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  name?: string;
  customOpportunityId?: number;
  opportunityName?: string;
  remark?: string;
  startTime?: number;
  status?: number;
  items?: any;
  stopList?: StopContractListModel[];
  attName?: string;
  signTime?: string;
};
export type CustomerChangeListModel = {
  changeItems?: ChangeItemsModel;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  description?: string;
  effectiveTime?: number;
  id?: number;
  name?: string;
  code?: string;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  reason?: string;
  remark?: string;
  status?: number;
  startTime?: number;
  endTime?: number;
  contractId?: number;
  entryId?: string;
  entryName?: string;
  customName?: string;
  electricityPrice?: number;
  electricityQty?: number;
  customId?: number;
  contractStatus?: number;
  customOpportunityId?: string;
  opportunityName?: string;
  signTime?: string;
  attName?: string;
};
// 合同终止列表
export type StopContractListModel = {
  contractId?: number;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  effectiveTime?: number;
  id?: number;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  reason?: string;
  status?: number;
  stopType?: number;
};
export type ChangeItemsModel = {
  fieldName?: string;
  newValue?: string;
  oldValue?: string;
};

// 电力用户扩展资料
export type CustomerContractListModel = {
  agentType?: number;
  annualElectricity?: number;
  customerChannel?: number;
  customerSource?: number;
  greenDemand?: string;
  id?: number;
  middlemanId?: number;
};
// 跟进记录列表
export type CustomerFollowListModel = {
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  customId?: number;
  customName?: string;
  electricityDemand?: string;
  entourage?: string;
  followLink?: string;
  followTime?: number;
  followerId?: number;
  followerName?: string;
  id?: number;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  otherDemand?: string;
  remark?: string;
  status?: number;
  title?: string;
  type?: number;
};
// 客户动态列表;
export type CustomerLogListModel = {};
// 居间商扩展资料;
export type CustomerMiddlemanModel = {
  annualProxyElectricity?: number;
  id?: number;
};
// 商机列表;
export type CustomerOpportunityListModel = {
  areaId?: number;
  areaName?: string;
  content?: string;
  contractElectricityQty?: number;
  createTime?: number;
  creatorId?: number;
  creatorNam?: string;
  customId?: number;
  customName?: string;
  existElectricityQty?: number;
  followStage?: number;
  followType?: number;
  followerId?: string;
  followerName?: string;
  id?: number;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  marketingAreaName?: string;
  otherDemand?: string;
  predictTradeDate?: string;
  remark?: string;
  status?: number;
  title?: string;
  yearlyElectricityQty?: number;
};
// 增值服务列表;
export type CustomerValueAddedListModel = {
  actualAmount?: number;
  createTime?: number;
  creatorId?: number;
  creatorName?: string;
  customId?: number;
  customName?: string;
  estimatedAmount?: number;
  id?: string;
  implementationContent?: string;
  implementationDate?: number;
  lastUpdateTime?: number;
  lastUpdaterId?: number;
  lastUpdaterName?: string;
  managerId?: number;
  managerName?: string;
  remark?: string;
  responsibleId?: number;
  responsibleName?: string;
  status?: number;
  title?: string;
};
// 标签列表
export type CustomerTagListModel = {
  customId?: number;
  id?: number;
  tagType?: string;
  tagValueId?: number;
  tagName?: string;
};
export interface CustomerResultModel {
  accountList?: CustomerAccountListModel[];
  basic?: CustomerBasicModel;
  bindList?: CustomerBindListModel[];
  contactList?: CustomerContactListModel[];
  contractList?: CustomerContractModel[];
  electricity?: CustomerContractListModel;
  followList?: CustomerFollowListModel[];
  logList?: CustomerLogListModel[];
  middleman?: CustomerMiddlemanModel;
  opportunityList?: CustomerOpportunityListModel[];
  valueAddedList?: CustomerValueAddedListModel[];
  tagList?: CustomerTagListModel[];
  tagNames?: string;
}

export interface CustomerListResultModel {
  id: number | string;
  name: string;
  agentType?: string | number;
  alias?: string;
  annualElectricity?: string | number;
  annualProxyElectricity?: string | number;
  areaId?: string;
  areaName?: string;
  bankAccount?: string | number;
  bankName?: string;
  contact?: string;
  createTime?: string;
  creatorId?: string;
  creatorName?: string;
  customGrade?: number;
  customIdentity?: number;
  customType?: number;
  customerChannel?: string;
  customerSource?: number;
  description?: string;
  followerId?: string;
  followerName?: string;
  formerName?: string;
  greenDemand?: number;
  isOpenSea?: boolean;
  isOutside?: boolean;
  isSign?: boolean;
  lastUpdateTime?: string;
  lastUpdaterId?: string;
  middlemanId?: string;
  ownership?: string;
  registeredCapital?: string;
  socialCreditCode?: string;
  status?: number;
  terminationDate?: string;
  effectiveDate?: number;
  electricityNature?: number;
  electricalNature?: number;
  industryId?: number;
}

// 合同数量统计;
export type GETContractTotalModel = {
  due30Count: number;
  due60Count: number;
  due90Count: number;
  executingCount: number;
  expiredCount: number;
  notStartedCount: number;
};

// 销售人员区域数量统计;
export type GetUserCountModel = {
  areaCount: number;
  userId: string;
  userName: string;
};

// 通过用户id查询区域ID列表;
export type GetSaleUserByIdModel = {
  areaIds: string[];
  userId: string;
};

// 商机转化漏斗;
export type GetOpportunityTotalModel = {
  count: number;
  id: number;
  name: string;
};

// 商机转化漏斗;
export type GetContractSumModel = {
  entryId: string;
  entryName: string;
  sumElectricityQty: number;
};

// 客户分级统计;
export type GetGradeCountModel = {
  customCount: number;
  customGradeId: number;
  customGradeName: string;
};

// 客户经理维护客户电量统计
export type GetElectricitySumModel = {
  followerId: string;
  followerName: string;
  sumAnnualElectricity: number;
};

// 客户档案分页查询请求参数
export type CustomerPageModel = BaseRequestParams & {
  customIdentity?: number;
  name?: string;
};

// 用电户号分页查询请求参数
export type ElectricityAccountPageModel = BaseRequestParams & {
  customName?: string;
  accountNo?: string;
  address?: string;
};

// 跟进记录分页查询请求参数
export type FollowListPageModel = BaseRequestParams & {
  title?: string;
  customName?: string;
  followerName?: string;
  customId?: string;
};

// 增值服务分页查询请求参数
export type ValueAddedListPageModel = BaseRequestParams & {
  title?: string;
  customName?: string;
  managerName?: string;
};
// 商机管理分页查询请求参数
export type CustomerOpportunityListPageModel = BaseRequestParams & {
  title?: string;
  customName?: string;
  followerName?: string;
  followStage?: number;
  customId?: string;
};
// 合同管理分页查询请求参数
export type CustomerContractListPageModel = BaseRequestParams & {
  code?: string;
  customName?: string;
  name?: string;
};

// 表计电量查询请求参数
export type CustomerPowerListPageModel = BaseRequestParams & {
  accountNo?: string;
  customId?: number;
  endReadDate?: number;
  meterCode?: string;
  startReadDate?: number;
};

export type CustomerPortraitLoadModel = {
  customId: number | string;
  dateType: number;
  start: number;
  end: number;
};

export type CustomerSumModel = {
  customName: string;
  sumElectricityQty: number;
};

export type AreaSumModel = {
  areaName: string;
  customCount: number;
  sumElectricityQty: number;
};
