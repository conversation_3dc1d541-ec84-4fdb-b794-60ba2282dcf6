<template>
  <div class="app-card">
    <div class="card-header">
      <div>新增</div>
      <div>
        <el-button @click="goBackAndClearStack">返回</el-button>
        <el-button type="primary" @click="submit(ruleFormRef)">保存</el-button>
      </div>
    </div>
    <div class="m-[20px]">
      <div class="font-bold mb-[10px]">基本信息</div>
      <el-form ref="ruleFormRef" :rules="dataFormRules" :model="formInline" label-width="160px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="用户名称：" prop="basic.name">
              <el-input maxlength="30" show-word-limit v-model="formInline.basic.name" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否居间商：">
              <el-radio-group @change="handleRadioChange" v-model="formInline.basic.customIdentity" class="ml-4">
                <el-radio :label="2">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业性质：" prop="ownership">
              <DictSelect v-model="formInline.basic.ownership" :clearable="true" dict-code="ownership" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="所在地区：" prop="post">
              <el-tree-select default-expand-all v-model="formInline.basic.areaId" check-strictly style="width: 100%"
                :props="{ children: 'children', label: 'name', value: 'id' }" placeholder="请选择" :data="treeData"
                @node-click="handleAreaChange" :render-after-expand="false" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="曾用名：" prop="birthday">
              <el-input maxlength="30" show-word-limit v-model="formInline.basic.formerName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年度代理电量：" v-if="showInput === 2" prop="middleman.annualProxyElectricity">
              <el-input maxlength="10" show-word-limit v-model="formInline.middleman.annualProxyElectricity"
                placeholder="请输入">
                <template #append>MWh</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="代理生效日期：">
              <el-date-picker style="width: 100%" v-model="formInline.basic.effectiveDate" type="datetime"
                placeholder="请选择" format="YYYY/MM/DD" value-format="x" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="代理结束日期：" prop="fax">
              <el-date-picker style="width: 100%" v-model="formInline.basic.terminationDate" type="datetime"
                placeholder="请选择" format="YYYY/MM/DD" value-format="x" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="代理电量：" maxlength="10" show-word-limit prop="basic.monthlyAverageElectricity">
              <el-input maxlength="10" show-word-limit v-model="formInline.basic.monthlyAverageElectricity"
                placeholder="请输入">
                <template #append>MWh</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="代理类型：" v-if="showInput === 1">
              <DictSelect v-model="formInline.electricity.agentType" :clearable="true" dict-code="agentType" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="showInput === 1">
            <el-form-item label="客户来源：" prop="customerSource">
              <DictSelect v-model="formInline.electricity.customerSource" :clearable="true"
                dict-code="customerSource" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="showInput === 1">
            <el-form-item label="绿电需求：">
              <el-input maxlength="10" show-word-limit v-model="formInline.electricity.greenDemand" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="统一社会信用代码：" prop="basic.socialCreditCode">
              <el-input maxlength="18" show-word-limit v-model="formInline.basic.socialCreditCode" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户银行：">
              <el-input maxlength="18" show-word-limit v-model="formInline.basic.bankName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户账号：">
              <el-input maxlength="20" show-word-limit v-model="formInline.basic.bankAccount" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="注册资本：">
              <el-input maxlength="18" show-word-limit v-model="formInline.basic.registeredCapital" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属集团：">
              <el-input maxlength="10" show-word-limit v-model="formInline.basic.membershipGroup" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户状态：" prop="wechat">
<!--              <DictSelect v-model="formInline.basic.isSign" :clearable="true" dict-code="customStatus" />-->
              <el-select v-model="formInline.basic.isSign" placeholder="请选择" clearable>
                <el-option label="已签约" :value="true"></el-option>
                <el-option label="未签约" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="法定代表人：">
              <el-input maxlength="10" show-word-limit v-model="formInline.basic.legalRepresentative"
                placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="登记机关：">
              <el-input maxlength="20" show-word-limit v-model="formInline.basic.registrationAuthority"
                placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="登记状态：">
              <DictSelect v-model="formInline.basic.registrationStatus" :clearable="true"
                dict-code="registrationStatus" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="经营范围：">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="4"
                v-model="formInline.basic.businessScope" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="营业期限：">
              <el-input v-model="formInline.basic.businessTerm" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属行业：">
              <DictSelect v-model="formInline.basic.industryId" :clearable="true" dict-code="industry" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用电性质：" prop="email">
              <DictSelect v-model="formInline.basic.electricalNature" :clearable="true" dict-code="electricityNature" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="行业分类：" prop="email">
              <el-input
                v-model="formInline.basic.registeredCapital"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户经理：" prop="email">
              <!-- <el-input v-model="formInline.basic.followerName" placeholder="请输入" /> -->
              <el-select style="width: 100%" @change="handleSelect" v-model="formInline.basic.followerId" filterable clearable
                placeholder="请选择">
                <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
<!--            <el-form-item label="是否公海：">-->
<!--              <el-radio-group v-model="formInline.basic.isOpenSea" class="ml-4">-->
<!--                <el-radio :label="true">是</el-radio>-->
<!--                <el-radio :label="false">否</el-radio>-->
<!--              </el-radio-group>-->
<!--            </el-form-item>-->
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户分级：">
              <DictSelect v-model="formInline.basic.customGrade" :clearable="true" dict-code="customGrade" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="用户标签：">
              <el-input @click="selectVisible = true" v-model="formInline.tagNames" placeholder="请选择" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="企业备注：" prop="qq">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="4" v-model="formInline.basic.description"
                placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="font-bold mt-[20px] mb-[10px]">联系人</div>
      <el-form class="contacts-form" v-for="(item, index) in formInline.contactList" :key="index" :model="item"
        label-width="160px" :rules="rules">
        <div class="flex justify-end mb-[10px]">
          <el-button size="small" v-if="formInline.contactList.length !== 0" @click="handleDel(item)">删除</el-button>
        </div>
        <el-row>
          <el-col :span="8">
            <el-form-item label="姓名：" prop="name">
              <el-input v-model="item.name" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别：" prop="sex">
              <el-radio-group v-model="item.sex" class="ml-4">
                <el-radio :label="1">男</el-radio>
                <el-radio :label="2">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="角色：" prop="role">
              <el-input v-model="item.role" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="部门职务：" prop="post">
              <el-input v-model="item.post" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生日：" prop="birthday">
              <el-date-picker v-model="item.birthday" type="date" placeholder="请选择" format="YYYY/MM/DD"
                value-format="x" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号码：" prop="mobile">
              <el-input v-model="item.mobile" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="固定电话：" prop="fixedTelephone">
              <el-input v-model="item.fixedTelephone" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="传真号码：" prop="fax">
              <el-input v-model="item.fax" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="微信号码：" prop="wechat">
              <el-input v-model="item.wechat" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="QQ号码：" prop="qq">
              <el-input v-model="item.qq" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电子邮箱：" prop="email">
              <el-input v-model="item.email" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="flex justify-end pb-[20px]">
        <el-button type="primary" @click="handleAdd">新增联系人</el-button>
        <!-- <el-button v-if="formInline.contactList.length !== 1" @click="handleDel"
          >删除联系人</el-button
        > -->
      </div>
    </div>
    <el-dialog width="60%" append-to-body v-model="selectVisible" title="标签选择">
      <tag-select @change="handleChange" />
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import TagSelect from "@/components/Core/TagSelect/index.vue";
import type { FormInstance } from "element-plus";
import { CustomerResultModel } from "@/model/customerModel";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, dayjs } from "element-plus";
import {
  saveCustomerDataApi,
  getAreaUserIdApi
} from "@/api/customer-management/index";
import { usePowerCustomer } from "@/hooks/customer/usePowerCustomer";
defineOptions({
  name: "CustomerCreate"
});
import {
  getAllSalesmanList, //获取营销人员列表
  batchUpdateSaleUser, //添加营销人员用户关联
} from '@/api'
import { cloneDeep } from 'lodash'
const optionsList = ref<any>([])
async function getAllSalesmanListInfo() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
}
const ruleFormRef = ref<FormInstance>();
const { push, go } = useRouter();
const { query } = useRoute();
const selectVisible = ref<boolean>(false);
const showInput = ref<number>(1);
const checkNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  if (!Number(value)) {
    callback(new Error("请输入数字"));
  } else {
    callback();
  }
};
const checkNumAndLetter = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  if (!/^[0-9a-zA-Z]+$/.test(value)) {
    callback(new Error("请输入数字或字母"));
  } else {
    callback();
  }
};
const checkMobile = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  const regMobile =
    /^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$/;
  if (!regMobile.test(value)) {
    callback(new Error("请输入合法的手机号"));
  } else {
    callback();
  }
};
const checkEmail = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  const regEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
  if (!regEmail.test(value)) {
    callback(new Error("请输入合法的邮箱"));
  } else {
    callback();
  }
};
const dataFormRules = {
  "basic.name": [
    {
      required: true,
      message: "用户名称是必填项",
      trigger: "blur"
    }
  ],
  "middleman.annualProxyElectricity": [
    {
      required: true,
      message: "年度代理电量是必填项",
      trigger: "blur"
    }
  ],
  "basic.monthlyAverageElectricity": [
    {
      validator: checkNum,
      trigger: "blur"
    }
  ],
  "basic.socialCreditCode": [
    {
      validator: checkNumAndLetter,
      trigger: "blur"
    }
  ]
};
const rules = {
  mobile: [
    {
      validator: checkMobile,
      trigger: "blur"
    }
  ],
  email: [
    {
      validator: checkEmail,
      trigger: "blur"
    }
  ]
};
const {
  getDictLabelByCode,
  getCityTreeData,
  treeData,
  getUserList
} = usePowerCustomer(false);
console.log(treeData);
const formInline = ref<CustomerResultModel>({
  basic: {
    name: "",
    isSign: false,
    ownership: undefined,
    areaId: "",
    followerId: "",
    formerName: "",
    annualElectricity: undefined,
    customGrade: undefined,
    industryId: undefined,
    terminationDate: undefined,
    effectiveDate: undefined,
    status: 0,
    socialCreditCode: "",
    bankName: "",
    bankAccount: "",
    registeredCapital: "",
    membershipGroup: "",
    greenDemand: "",
    followerName: "",
    description: "",
    isOpenSea: undefined,
    customIdentity: 1,
    registrationNo: "",
    legalRepresentative: "",
    businessTerm: "",
    businessRegistrationDate: undefined,
    issueDate: undefined,
    registrationAuthority: "",
    registrationStatus: undefined,
    registeredAddress: "",
    businessScope: "",
    electricalNature: undefined,
    mainBusiness: "",
    monthlyAverageElectricity: undefined
  },
  electricity: {
    agentType: undefined,
    annualElectricity: undefined,
    customerSource: undefined,
    greenDemand: ""
  },
  middleman: {
    annualProxyElectricity: undefined
  },
  tagNames: "",
  tagList: [],
  contactList: [
    {
      id: undefined,
      sex: undefined,
      name: "",
      role: "",
      post: "",
      birthday: undefined,
      mobile: "",
      fixedTelephone: "",
      fax: "",
      wechat: "",
      qq: "",
      email: "",
      customName: "",
      customId: undefined
    }
  ]
});
function handleRadioChange(value) {
  showInput.value = value;
}
function handleAdd() {
  const item = {
    id: undefined,
    sex: undefined,
    name: "",
    role: "",
    post: "",
    birthday: undefined,
    mobile: "",
    fixedTelephone: "",
    fax: "",
    wechat: "",
    qq: "",
    email: "",
    customName: "",
    customId: undefined
  };
  formInline.value.contactList.push(item);
}
function handleDel(row) {
  const index = formInline.value.contactList.findIndex(i => i.id == row.id);
  if (index !== -1) {
    formInline.value.contactList.splice(index, 1);
  }
}
async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (
        dayjs(formInline.value.basic.effectiveDate).valueOf() >
        dayjs(formInline.value.basic.terminationDate).valueOf()
      ) {
        ElMessage({
          message: "代理生效日期必须早于代理结束日期",
          type: "error"
        });
        return;
      }
      let newformInline = cloneDeep(formInline.value)
      delete formInline.value.basic.followerName
      delete formInline.value.basic.followerId
      const res = await saveCustomerDataApi(formInline.value);
      let userId
      if (res) {
        userId = res.data.basic.id
      }
      let areaName
      function getareaName(array) {
        array.forEach((item: any) => {
          if (item.id == formInline.value.basic.areaId) {
            areaName = item.name
          } else if (item.children) {
            getareaName(item.children)
          }
        });
        return areaName
      }
      if (formInline.value.basic.areaId) {
        getareaName(treeData.value)
      }
      await batchUpdateSaleUser([
        {
          userId,
          type: newformInline.basic.isOpenSea ? 1 : 2,
          userName: newformInline.basic.name,
          areaId: newformInline.basic.areaId,
          areaName: areaName,
          customerSource: newformInline.electricity.customerSource,
          annualElectricity: newformInline.basic.monthlyAverageElectricity * 12,
          saleName: newformInline.basic.followerName,
          customSaleId: newformInline.basic.followerId
        }
      ])
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        goBackAndClearStack();
      } else {
        ElMessage({
          message: res.message,
          type: "error"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
function handleChange(data) {
  selectVisible.value = false;
  if (Array.isArray(data) && data.length) {
    getDictLabelByCode(data);
    formInline.value.tagNames = data.map(i => i.tagName).join("、");
    formInline.value.tagList = data.map(item => {
      return {
        ...item
      };
    });
  } else {
    formInline.value.tagNames = "";
  }
}
function handleSelect(data) {
  const name = optionsList.value.find(i => i.value == data).label;
  formInline.value.basic.followerName = name;
}
// 地区改变事件
async function handleAreaChange(data) {
  const res = await getAreaUserIdApi(data.id);
  if (res.data) {
    handleSelect(res.data);
    formInline.value.basic.followerId = res.data;
  }
}
function goBackAndClearStack() {
  if (window.history.length <= 1) {
    push({ path: "/" });
    return false;
  } else {
    go(-1);
  }
}
onMounted(() => {
  formInline.value.basic.isOpenSea = query.isOpenSea === "true" ? true : false;
  getCityTreeData();
  getUserList();
  getAllSalesmanListInfo()
});
</script>
<style lang="scss" scoped>
.contacts-form {
  margin-top: 20px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 20px;

  &:last-of-type {
    border: none;
  }
}
</style>
