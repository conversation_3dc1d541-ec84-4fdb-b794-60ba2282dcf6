<template>
  <div>
    <el-dialog v-model="dialogVisible" title="合同变更" destroy-on-close width="60%">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="合同变更内容" name="1">
          <el-form ref="ruleFormRef" :rules="dataFormRules" :model="formInline" label-width="160px">
            <div class="my-5">基础信息</div>
            <el-row>
              <el-col :span="8">
                <el-form-item label="合同编号：" prop="code">
                  <el-input maxlength="20" show-word-limit v-model="formInline.code" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同名称：" prop="name">
                  <el-input maxlength="30" show-word-limit v-model="formInline.name" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合同开始时间：" prop="startTime">
                  <el-date-picker style="width: 100%" v-model="formInline.startTime" type="date" placeholder="请选择"
                    value-format="x" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="合同结束时间：" prop="endTime">
                  <el-date-picker style="width: 100%" v-model="formInline.endTime" type="date" placeholder="请选择"
                    value-format="x" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="跟踪人：">
                  <el-select style="width: 100%" v-model="formInline.entryId" filterable @change="handleSelect"
                    placeholder="请选择">
                    <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="甲方：">
                  <el-input maxlength="20" show-word-limit @click="selectVisible = true" v-model="formInline.customName"
                    placeholder="请选择" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="商机：">
                  <el-input @click="selectBusinessVisible = true" v-model="formInline.opportunityName"
                    placeholder="请选择" />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="my-5">结算信息</div>
            <el-row>
              <el-col :span="8">
                <el-form-item label="代理电价：" prop="electricityPrice">
                  <el-input maxlength="10" show-word-limit v-model="formInline.electricityPrice" placeholder="请输入">
                    <template #append>元/MWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="总电量：" prop="electricityQty">
                  <el-input maxlength="10" show-word-limit v-model="formInline.electricityQty" placeholder="请输入">
                    <template #append>MWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="my-5">其他信息</div>
            <el-col :span="8">
              <el-form-item label="合同附件：" prop="fileList1">
                <el-upload v-model:file-list="fileList1" with-credentials :headers="header" :data="uploadData1"
                  :on-preview="handleDownload" :action="actionUrl" :on-remove="handleRemove"
                  :before-upload="beforeUpload">
                  <el-button type="primary">点击上传</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持所有格式，且大小不超过10M
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="补充协议：">
                <el-upload v-model:file-list="fileList2" with-credentials :headers="header" :data="uploadData2"
                  :on-preview="handleDownload" :on-remove="handleRemove" :action="actionUrl"
                  :before-upload="beforeUpload">
                  <el-button type="primary">点击上传</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持所有格式，且大小不超过10M
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="添加变更内容" name="2">
          <el-form :model="formInline" label-width="160px">
            <el-row>
              <el-col :span="24">
                <el-form-item label="主要变更说明：" prop="description">
                  <el-input maxlength="30" show-word-limit type="textarea" v-model="formInline.description"
                    placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="变更原因：" prop="reason">
                  <el-input maxlength="30" show-word-limit type="textarea" v-model="formInline.reason"
                    placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="变更生效日期：" prop="effectiveTime">
                  <el-date-picker style="width: 100%" v-model="formInline.effectiveTime" type="date" placeholder="请选择"
                    value-format="x" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="其他附件：">
                  <el-upload v-model:file-list="fileList3" with-credentials :headers="header" :data="uploadData3"
                    :on-preview="handleDownload" :on-remove="handleRemove" :action="actionUrl"
                    :before-upload="beforeUpload">
                    <el-button type="primary">点击上传</el-button>
                    <template #tip>
                      <div class="el-upload__tip">
                        支持所有格式，且大小不超过10M
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submit(ruleFormRef)">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog width="60%" append-to-body destroy-on-close v-model="selectVisible" title="客户选择">
      <customer-select @close="handleClose" />
    </el-dialog>
    <el-dialog width="70%" append-to-body destroy-on-close v-model="selectBusinessVisible" title="商机选择">
      <business-opportunity-select @change="handleSelectBusiness" />
      <!-- <div class="flex justify-end mt-[40px]">
        <el-button type="primary" @click="selectBusinessVisible = false"
          >确认选择</el-button
        >
      </div> -->
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "ChangeForm"
});
import { ref, nextTick } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import CustomerSelect from "@/components/Core/CustomerSelect/index.vue";
import { getAllUserListApi, getFileListApi, getUnqueIdApi } from "@/api/user";
import type { UploadUserFile } from "element-plus";
import BusinessOpportunitySelect from "@/components/Core/BusinessOpportunitySelect/index.vue";
import {
  getContractByIdApi,
  changeContractApi
} from "@/api/customer-management/index";
import { useFileAction } from "@/hooks/fileAction/useFileAction";
import { CustomerChangeListModel } from "@/model/customerModel";
import {
  getAllSalesmanList, //获取营销人员列表
} from '@/api'
import dayjs from "dayjs";
const emit = defineEmits(["update"]);
const {
  BaseUrl,
  handleDownload,
  header,
  handleRemove,
  beforeUpload,
  actionUrl
} = useFileAction();
// 合同协议上传额外参数
const uploadData1 = ref({
  type: "1",
  id: undefined
});
// 补充协议上传额外参数
const uploadData2 = ref({
  type: "2",
  id: undefined
});
// 其他附件上传额外参数
const uploadData3 = ref({
  type: "3",
  id: undefined
});
const selectBusinessVisible = ref<boolean>(false);

const ruleFormRef = ref<FormInstance>();
const checkNum = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请输入"));
  }
  if (!Number(value)) {
    callback(new Error("请输入数字"));
  } else {
    callback();
  }
};

function validateFileList(rule, value, callback) {
  nextTick(() => {
    console.log(value,fileList1.value)
    if (fileList1.value.length === 0) {
      callback(new Error('请上传至少一个文件'));
    } else {
      callback();
    }
  })
}
// 表单校验
const dataFormRules = {
  code: [
    {
      required: true,
      message: "合同编号是必填项",
      trigger: "blur"
    }
  ],
  name: [
    {
      required: true,
      message: "合同名称是必填项",
      trigger: "blur"
    }
  ],
  startTime: [
    {
      required: true,
      message: "合同开始时间是必填项",
      trigger: "blur"
    }
  ],
  opportunityName: [
    {
      required: true,
      message: "商机是必填项",
      trigger: "blur"
    }
  ],
  endTime: [
    {
      required: true,
      message: "合同结束时间是必填项",
      trigger: "blur"
    }
  ],
  electricityPrice: [
    {
      validator: checkNum,
      required: true,
      trigger: "blur"
    }
  ],
  electricityQty: [
    {
      validator: checkNum,
      required: true,
      trigger: "blur"
    }
  ],
  fileList1: [
    {
      // required: true,
      // validator: validateFileList,
      trigger: "change"
    }
  ]
};
const activeName = ref("1");
// 合同跟进人下拉
const optionsList = ref([]);
// 合同附件列表
const fileList1 = ref<UploadUserFile[]>([]);
// 补充附件列表
const fileList2 = ref<UploadUserFile[]>([]);
//其他附件列表
const fileList3 = ref<UploadUserFile[]>([]);

const dialogVisible = ref<boolean>(false);
const formInline = ref<CustomerChangeListModel>({
  id: undefined,
  name: "",
  code: "",
  contractId: undefined,
  startTime: undefined,
  endTime: undefined,
  electricityPrice: undefined,
  electricityQty: undefined,
  customName: "",
  customId: undefined,
  description: "",
  reason: "",
  entryId: undefined,
  entryName: undefined,
  effectiveTime: undefined
});
const selectVisible = ref<boolean>(false);
// 获取所有用户
async function getUserList() {
  const res = await getAllSalesmanList()
  if (res && res.length) {
    optionsList.value = res.map((item: any) => {
      return {
        label: item.name,
        value: item.id
      }
    })
  }
}
// async function getUserList() {
//   const res = await getAllUserListApi();
//   if (res.data) {
//     optionsList.value = res.data.map(item => {
//       return {
//         label: item.name,
//         value: String(item.id)
//       };
//     });
//   }
// }
function handleSelect(data) {
  const name = optionsList.value.find(i => i.value == data).label;
  formInline.value.entryName = name;
}
// 商机确认选择事件
function handleSelectBusiness(row) {
  // console.log(row, 'row');
  selectBusinessVisible.value = false;
  formInline.value.customOpportunityId = row.id;
  formInline.value.customOpportunityTitle = row.title;
}
function handleClick() { }
// 客户确认选择弹框事件
function handleClose(row) {
  selectVisible.value = false;
  formInline.value.customId = row.id;
  formInline.value.customName = row.name;
}
// 查询附件
async function getFileList(type, id) {
  const file = await getFileListApi(type, id);
  const fileArr: any[] = [];
  if (file.data.length) {
    file.data.forEach(item => {
      fileArr.push({
        status: "done",
        name: item.fileName,
        uid: item.id,
        url: BaseUrl + "/system/annex/download?id=" + item.id
      });
    });
    if (type === "1") {
      fileList1.value = fileArr;
    } else if (type === "2") {
      fileList2.value = fileArr;
    } else if (type === "3") {
      fileList3.value = fileArr;
    }
  }
}
async function getUploadFileId() {
  // 生成上传的唯一id
  const res = await getUnqueIdApi();
  uploadData1.value.id = res.data;
  uploadData2.value.id = res.data;
  uploadData3.value.id = res.data;
}
async function handleCreate(id) {
  fileList1.value = [];
  fileList2.value = [];
  fileList3.value = [];
  dialogVisible.value = true;
  await nextTick();
  ruleFormRef.value.resetFields();
  activeName.value = "1";
  await getUploadFileId();
  await getUserList();
  await getDetail(id);
  getFileList("1", id);
  getFileList("2", id);
  getFileList("3", id);
  // uploadData1.value.id = formInline.value.id;
  // uploadData2.value.id = formInline.value.id;
  // uploadData3.value.id = formInline.value.id;
}
async function getDetail(id) {
  const res = await getContractByIdApi(id);
  if (res.data) {
    formInline.value = { ...res.data };
    formInline.value.startTime = Number(formInline.value.startTime);
    formInline.value.endTime = Number(formInline.value.endTime);
    formInline.value.effectiveTime = Number(formInline.value.effectiveTime);
    formInline.value.contractId = res.data.id;
    formInline.value.id = uploadData1.value.id;
  }
}
async function submit(formEl: FormInstance | undefined) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (
        dayjs(formInline.value.startTime).valueOf() >
        dayjs(formInline.value.endTime).valueOf()
      ) {
        ElMessage({
          message: "合同开始时间不能大于合同结束时间！",
          type: "error"
        });
        return;
      }
      const res = await changeContractApi(formInline.value);
      if (res.code === "200") {
        ElMessage({
          message: "操作成功",
          type: "success"
        });
        emit("update");
        // getList();
        dialogVisible.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "error"
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
}
defineExpose({
  handleCreate
});
</script>

<style lang="scss" scoped></style>
